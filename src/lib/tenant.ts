import { supabase } from '@/integrations/supabase/client';

export interface Club {
  id: string;
  name: string;
  slug: string;
  custom_domain?: string;
  subdomain?: string;
  logo_url?: string;
  description?: string;
  settings?: any;
  is_active: boolean;
}

export interface TenantContext {
  club: Club | null;
  isLoading: boolean;
  error: string | null;
}

class TenantResolver {
  private currentClub: Club | null = null;
  private isLoading = false;

  async resolveClub(): Promise<Club | null> {
    if (typeof window === 'undefined') return null;
    
    this.isLoading = true;
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;

    try {
      // 1. Check for custom domain
      const customDomainClub = await this.getClubByCustomDomain(hostname);
      if (customDomainClub) {
        this.currentClub = customDomainClub;
        return customDomainClub;
      }

      // 2. Check for subdomain
      const subdomainMatch = hostname.match(/^([^.]+)\./);
      if (subdomainMatch) {
        const subdomain = subdomainMatch[1];
        // Skip meta admin subdomain
        if (subdomain !== 'admin') {
          const subdomainClub = await this.getClubBySubdomain(subdomain);
          if (subdomainClub) {
            this.currentClub = subdomainClub;
            return subdomainClub;
          }
        }
      }

      // 3. Check for path-based routing (/c/{club})
      const pathMatch = pathname.match(/^\/c\/([^\/]+)/);
      if (pathMatch) {
        const clubSlug = pathMatch[1];
        const pathClub = await this.getClubBySlug(clubSlug);
        if (pathClub) {
          this.currentClub = pathClub;
          return pathClub;
        }
      }

      // 4. No club found
      this.currentClub = null;
      return null;
    } catch (error) {
      console.error('Error resolving tenant:', error);
      this.currentClub = null;
      return null;
    } finally {
      this.isLoading = false;
    }
  }

  private async getClubByCustomDomain(domain: string): Promise<Club | null> {
    const { data, error } = await supabase
      .from('clubs')
      .select('*')
      .eq('custom_domain', domain)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error fetching club by custom domain:', error);
      return null;
    }

    return data;
  }

  private async getClubBySubdomain(subdomain: string): Promise<Club | null> {
    const { data, error } = await supabase
      .from('clubs')
      .select('*')
      .eq('subdomain', subdomain)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error fetching club by subdomain:', error);
      return null;
    }

    return data;
  }

  private async getClubBySlug(slug: string): Promise<Club | null> {
    const { data, error } = await supabase
      .from('clubs')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error fetching club by slug:', error);
      return null;
    }

    return data;
  }

  getCurrentClub(): Club | null {
    return this.currentClub;
  }

  isMetaAdmin(): boolean {
    if (typeof window === 'undefined') return false;
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;

    const isMetaAdminResult = hostname.startsWith('admin.') ||
                             hostname.includes('admin') ||
                             pathname.startsWith('/meta-admin');

    console.log('🔍 tenantResolver.isMetaAdmin():', {
      hostname,
      pathname,
      startsWithAdmin: hostname.startsWith('admin.'),
      includesAdmin: hostname.includes('admin'),
      pathStartsWithMetaAdmin: pathname.startsWith('/meta-admin'),
      result: isMetaAdminResult
    });

    return isMetaAdminResult;
  }

  getClubBasePath(): string {
    if (!this.currentClub) return '';
    
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;
    
    // Custom domain or subdomain - no prefix needed
    if (this.currentClub.custom_domain === hostname || 
        hostname.startsWith(`${this.currentClub.subdomain}.`)) {
      return '';
    }
    
    // Path-based routing
    if (pathname.startsWith(`/c/${this.currentClub.slug}`)) {
      return `/c/${this.currentClub.slug}`;
    }
    
    return '';
  }

  buildClubUrl(path: string = ''): string {
    const club = this.getCurrentClub();
    if (!club) return path;

    const basePath = this.getClubBasePath();
    return `${basePath}${path}`;
  }
}

export const tenantResolver = new TenantResolver();

// Utility function to check if current user is super admin
export async function isSuperAdmin(): Promise<boolean> {
  const { data: userRoles, error } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
    .eq('role', 'super_admin');

  return !error && userRoles && userRoles.length > 0;
}

// Set tenant context for database operations (using PostgreSQL directly)
export async function setTenantContext(clubId: string | null) {
  if (clubId) {
    // This would need to be set via direct SQL if needed
    // For now, we'll handle tenant context in application logic
    console.log('Setting tenant context for club:', clubId);
  }
}