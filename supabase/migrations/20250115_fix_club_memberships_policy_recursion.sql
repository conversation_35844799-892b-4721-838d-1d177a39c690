-- Fix infinite recursion in club_memberships policies
-- This migration addresses the circular reference in the club admin policy
--
-- Problem: The policy "Club admins can manage their club memberships" was querying
-- the same club_memberships table it was protecting, causing infinite recursion.
--
-- Solution: Create a security definer function that can safely check club admin status
-- without triggering RLS policies, then use this function in the policy.

-- First, drop the problematic policy
DROP POLICY IF EXISTS "Club admins can manage their club memberships" ON public.club_memberships;

-- Create a security definer function to check club admin status
-- This function will run with elevated privileges to avoid recursion
CREATE OR REPLACE FUNCTION public.is_club_admin(user_id_param uuid, club_id_param uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.club_memberships cm
    WHERE cm.user_id = user_id_param
      AND cm.club_id = club_id_param
      AND cm.role = 'admin'
      AND cm.is_active = true
  );
$$;

-- Grant execute permission to public (authenticated users)
GRANT EXECUTE ON FUNCTION public.is_club_admin(uuid, uuid) TO public;

-- Create a new policy that uses the security definer function
CREATE POLICY "Club admins can manage their club memberships"
ON public.club_memberships
FOR ALL
TO public
USING (
  -- Allow access if user is a super admin
  has_role(auth.uid(), 'super_admin'::app_role)
  OR
  -- Allow access if user is an admin of this specific club
  public.is_club_admin(auth.uid(), club_id)
)
WITH CHECK (
  -- Same conditions for insert/update
  has_role(auth.uid(), 'super_admin'::app_role)
  OR
  public.is_club_admin(auth.uid(), club_id)
);

-- Add a comment explaining the fix
COMMENT ON FUNCTION public.is_club_admin(uuid, uuid) IS 
'Security definer function to check if a user is an admin of a specific club. 
This function prevents infinite recursion in RLS policies by running with elevated privileges.';

-- Verify the policies are working by testing the structure
-- (This is just a comment for documentation - the actual test would be done in application)
-- Test cases to verify:
-- 1. Super admins can access all club memberships
-- 2. Club admins can only access memberships for their clubs
-- 3. Regular users can only view their own memberships
-- 4. No infinite recursion occurs when querying club_memberships
